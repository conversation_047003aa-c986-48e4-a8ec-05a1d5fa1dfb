-- Migration 008: Add Login Security Features
-- This adds brute force protection and failed login tracking

-- Add security columns to doctors table
ALTER TABLE public.doctors 
ADD COLUMN failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
ADD COLUMN last_failed_login_at TIMESTAMPTZ NULL,
ADD COLUMN locked_until TIMESTAMPTZ NULL,
ADD COLUMN last_successful_login_at TIMESTAMPTZ NULL,
ADD COLUMN login_ip_address TEXT NULL,
ADD COLUMN password_changed_at TIMESTAMPTZ DEFAULT NOW();

-- Add security columns to admins table
ALTER TABLE public.admins 
ADD COLUMN failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
ADD COLUMN last_failed_login_at TIMESTAMPTZ NULL,
ADD COLUMN locked_until TIMESTAMPTZ NULL,
ADD COLUMN last_successful_login_at TIMESTAMPTZ NULL,
ADD COLUMN login_ip_address TEXT NULL,
ADD COLUMN password_changed_at TIMESTAMPTZ DEFAULT NOW();

-- Create indexes for efficient security queries
CREATE INDEX idx_doctors_failed_attempts ON public.doctors(failed_login_attempts) WHERE failed_login_attempts > 0;
CREATE INDEX idx_doctors_locked_until ON public.doctors(locked_until) WHERE locked_until IS NOT NULL;
CREATE INDEX idx_admins_failed_attempts ON public.admins(failed_login_attempts) WHERE failed_login_attempts > 0;
CREATE INDEX idx_admins_locked_until ON public.admins(locked_until) WHERE locked_until IS NOT NULL;

-- Create function to automatically unlock accounts after timeout
CREATE OR REPLACE FUNCTION unlock_expired_accounts()
RETURNS INTEGER AS $$
DECLARE
    unlocked_count INTEGER := 0;
BEGIN
    -- Unlock doctors with expired lockouts
    UPDATE public.doctors 
    SET 
        locked_until = NULL,
        failed_login_attempts = 0,
        last_failed_login_at = NULL
    WHERE 
        locked_until IS NOT NULL 
        AND locked_until < NOW();
    
    GET DIAGNOSTICS unlocked_count = ROW_COUNT;
    
    -- Unlock admins with expired lockouts
    UPDATE public.admins 
    SET 
        locked_until = NULL,
        failed_login_attempts = 0,
        last_failed_login_at = NULL
    WHERE 
        locked_until IS NOT NULL 
        AND locked_until < NOW();
    
    GET DIAGNOSTICS unlocked_count = unlocked_count + ROW_COUNT;
    
    RETURN unlocked_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to track successful logins
CREATE OR REPLACE FUNCTION record_successful_login(
    user_table TEXT,
    user_id UUID,
    ip_address TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    IF user_table = 'doctors' THEN
        UPDATE public.doctors 
        SET 
            failed_login_attempts = 0,
            last_failed_login_at = NULL,
            locked_until = NULL,
            last_successful_login_at = NOW(),
            login_ip_address = ip_address
        WHERE id = user_id;
    ELSIF user_table = 'admins' THEN
        UPDATE public.admins 
        SET 
            failed_login_attempts = 0,
            last_failed_login_at = NULL,
            locked_until = NULL,
            last_successful_login_at = NOW(),
            login_ip_address = ip_address
        WHERE id = user_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to track failed logins
CREATE OR REPLACE FUNCTION record_failed_login(
    user_table TEXT,
    user_id UUID,
    max_attempts INTEGER DEFAULT 5,
    lockout_duration_minutes INTEGER DEFAULT 15
)
RETURNS BOOLEAN AS $$
DECLARE
    current_attempts INTEGER;
    should_lock BOOLEAN := FALSE;
BEGIN
    IF user_table = 'doctors' THEN
        UPDATE public.doctors 
        SET 
            failed_login_attempts = failed_login_attempts + 1,
            last_failed_login_at = NOW(),
            locked_until = CASE 
                WHEN failed_login_attempts + 1 >= max_attempts 
                THEN NOW() + (lockout_duration_minutes || ' minutes')::INTERVAL
                ELSE locked_until
            END
        WHERE id = user_id
        RETURNING failed_login_attempts + 1 >= max_attempts INTO should_lock;
        
    ELSIF user_table = 'admins' THEN
        UPDATE public.admins 
        SET 
            failed_login_attempts = failed_login_attempts + 1,
            last_failed_login_at = NOW(),
            locked_until = CASE 
                WHEN failed_login_attempts + 1 >= max_attempts 
                THEN NOW() + (lockout_duration_minutes || ' minutes')::INTERVAL
                ELSE locked_until
            END
        WHERE id = user_id
        RETURNING failed_login_attempts + 1 >= max_attempts INTO should_lock;
    END IF;
    
    RETURN should_lock;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for security monitoring
CREATE VIEW security_monitoring AS
SELECT 
    'doctor' as user_type,
    id as user_id,
    email,
    name,
    failed_login_attempts,
    last_failed_login_at,
    locked_until,
    last_successful_login_at,
    login_ip_address,
    CASE 
        WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 'locked'
        WHEN failed_login_attempts > 0 THEN 'warning'
        ELSE 'normal'
    END as security_status
FROM public.doctors
WHERE failed_login_attempts > 0 OR locked_until IS NOT NULL

UNION ALL

SELECT 
    'admin' as user_type,
    id as user_id,
    email,
    name,
    failed_login_attempts,
    last_failed_login_at,
    locked_until,
    last_successful_login_at,
    login_ip_address,
    CASE 
        WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 'locked'
        WHEN failed_login_attempts > 0 THEN 'warning'
        ELSE 'normal'
    END as security_status
FROM public.admins
WHERE failed_login_attempts > 0 OR locked_until IS NOT NULL;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION unlock_expired_accounts() TO authenticated;
GRANT EXECUTE ON FUNCTION record_successful_login(TEXT, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION record_failed_login(TEXT, UUID, INTEGER, INTEGER) TO authenticated;
GRANT SELECT ON security_monitoring TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION unlock_expired_accounts() IS 'Automatically unlocks accounts with expired lockout periods';
COMMENT ON FUNCTION record_successful_login(TEXT, UUID, TEXT) IS 'Records successful login and resets security counters';
COMMENT ON FUNCTION record_failed_login(TEXT, UUID, INTEGER, INTEGER) IS 'Records failed login attempt and applies lockout if threshold exceeded';
COMMENT ON VIEW security_monitoring IS 'Provides overview of accounts with security issues for monitoring';