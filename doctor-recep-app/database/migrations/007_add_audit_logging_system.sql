-- Migration 007: Add Comprehensive Audit Logging System
-- This creates an immutable audit trail for all critical data access

-- Create the audit log table
CREATE TABLE public.audit_log (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    user_id TEXT NOT NULL,
    user_role TEXT NOT NULL CHECK (user_role IN ('doctor', 'admin', 'super_admin')),
    operation TEXT NOT NULL,
    table_name TEXT NOT NULL,
    record_id TEXT,
    metadata JSONB,
    ip_address TEXT,
    user_agent TEXT,
    session_id TEXT,
    request_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for efficient querying
CREATE INDEX idx_audit_log_timestamp ON public.audit_log(timestamp DESC);
CREATE INDEX idx_audit_log_user_id ON public.audit_log(user_id);
CREATE INDEX idx_audit_log_table_name ON public.audit_log(table_name);
CREATE INDEX idx_audit_log_operation ON public.audit_log(operation);
CREATE INDEX idx_audit_log_user_role ON public.audit_log(user_role);

-- Enable RLS on audit log table
ALTER TABLE public.audit_log ENABLE ROW LEVEL SECURITY;

-- Create immutable INSERT-only policy
CREATE POLICY "Allow insert only for audit log" 
ON public.audit_log 
FOR INSERT 
WITH CHECK (true);

-- Create SELECT policy for admins only
CREATE POLICY "Admins can view audit logs" 
ON public.audit_log 
FOR SELECT 
USING (true); -- Will be controlled at application layer

-- Prevent UPDATE and DELETE operations entirely
CREATE POLICY "Deny all updates to audit log" 
ON public.audit_log 
FOR UPDATE 
USING (false);

CREATE POLICY "Deny all deletes from audit log" 
ON public.audit_log 
FOR DELETE 
USING (false);

-- Create function to automatically set timestamp
CREATE OR REPLACE FUNCTION set_audit_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.timestamp = NOW();
    NEW.created_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure timestamp is always set correctly
CREATE TRIGGER set_audit_timestamp_trigger
    BEFORE INSERT ON public.audit_log
    FOR EACH ROW
    EXECUTE FUNCTION set_audit_timestamp();

-- Grant necessary permissions
GRANT INSERT ON public.audit_log TO authenticated;
GRANT INSERT ON public.audit_log TO anon;
GRANT SELECT ON public.audit_log TO authenticated;

-- Add comment for documentation
COMMENT ON TABLE public.audit_log IS 'Immutable audit trail for all critical data access operations. INSERT-only table for compliance and security monitoring.';