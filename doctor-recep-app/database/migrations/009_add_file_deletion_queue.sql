-- Migration 009: Add File Deletion Queue for Compliance
-- This creates a system for scheduling file deletions as part of the anonymization process

-- Create file deletion queue table
CREATE TABLE IF NOT EXISTS public.file_deletion_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    doctor_id UUID NOT NULL,
    file_url TEXT NOT NULL,
    file_type TEXT CHECK (file_type IN ('audio', 'image')) DEFAULT 'audio',
    scheduled_for TIMESTAMPTZ NOT NULL,
    anonymization_id UUID,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- Create indexes for efficient querying
CREATE INDEX idx_file_deletion_queue_scheduled_for ON public.file_deletion_queue(scheduled_for) WHERE status = 'pending';
CREATE INDEX idx_file_deletion_queue_doctor_id ON public.file_deletion_queue(doctor_id);
CREATE INDEX idx_file_deletion_queue_status ON public.file_deletion_queue(status);
CREATE INDEX idx_file_deletion_queue_anonymization_id ON public.file_deletion_queue(anonymization_id);

-- Enable RLS
ALTER TABLE public.file_deletion_queue ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admins can manage file deletion queue" 
ON public.file_deletion_queue 
FOR ALL 
USING (true); -- Will be controlled at application layer

-- Create function to automatically create the table if it doesn't exist
CREATE OR REPLACE FUNCTION create_file_deletion_queue_if_not_exists()
RETURNS VOID AS $$
BEGIN
    -- This function is called from the anonymization process
    -- The table creation is handled by this migration
    -- This function exists for compatibility
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_file_deletion_queue_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_file_deletion_queue_updated_at_trigger
    BEFORE UPDATE ON public.file_deletion_queue
    FOR EACH ROW
    EXECUTE FUNCTION update_file_deletion_queue_updated_at();

-- Create function to mark file deletion as failed
CREATE OR REPLACE FUNCTION mark_file_deletion_failed(
    deletion_id UUID,
    error_msg TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.file_deletion_queue 
    SET 
        status = 'failed',
        error_message = error_msg,
        attempts = attempts + 1,
        updated_at = NOW()
    WHERE id = deletion_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark file deletion as completed
CREATE OR REPLACE FUNCTION mark_file_deletion_completed(
    deletion_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.file_deletion_queue 
    SET 
        status = 'completed',
        processed_at = NOW(),
        updated_at = NOW()
    WHERE id = deletion_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to retry failed deletions
CREATE OR REPLACE FUNCTION retry_failed_file_deletions()
RETURNS INTEGER AS $$
DECLARE
    retry_count INTEGER := 0;
BEGIN
    -- Reset failed deletions that haven't exceeded max attempts
    UPDATE public.file_deletion_queue 
    SET 
        status = 'pending',
        error_message = NULL,
        updated_at = NOW()
    WHERE 
        status = 'failed' 
        AND attempts < max_attempts
        AND scheduled_for <= NOW();
    
    GET DIAGNOSTICS retry_count = ROW_COUNT;
    
    RETURN retry_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up old completed deletions
CREATE OR REPLACE FUNCTION cleanup_completed_file_deletions(
    older_than_days INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    cleanup_count INTEGER := 0;
BEGIN
    DELETE FROM public.file_deletion_queue 
    WHERE 
        status = 'completed' 
        AND processed_at < (NOW() - (older_than_days || ' days')::INTERVAL);
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    
    RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for file deletion monitoring
CREATE VIEW file_deletion_monitoring AS
SELECT 
    fdq.id,
    fdq.doctor_id,
    d.name as doctor_name,
    d.email as doctor_email,
    fdq.file_url,
    fdq.file_type,
    fdq.scheduled_for,
    fdq.status,
    fdq.attempts,
    fdq.max_attempts,
    fdq.error_message,
    fdq.created_at,
    fdq.updated_at,
    fdq.processed_at,
    CASE 
        WHEN fdq.status = 'pending' AND fdq.scheduled_for <= NOW() THEN 'due'
        WHEN fdq.status = 'pending' AND fdq.scheduled_for > NOW() THEN 'scheduled'
        WHEN fdq.status = 'failed' AND fdq.attempts >= fdq.max_attempts THEN 'failed_permanently'
        WHEN fdq.status = 'failed' AND fdq.attempts < fdq.max_attempts THEN 'failed_retryable'
        ELSE fdq.status
    END as effective_status
FROM public.file_deletion_queue fdq
LEFT JOIN public.doctors d ON fdq.doctor_id = d.id
ORDER BY fdq.scheduled_for ASC;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.file_deletion_queue TO authenticated;
GRANT EXECUTE ON FUNCTION create_file_deletion_queue_if_not_exists() TO authenticated;
GRANT EXECUTE ON FUNCTION mark_file_deletion_failed(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_file_deletion_completed(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION retry_failed_file_deletions() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_completed_file_deletions(INTEGER) TO authenticated;
GRANT SELECT ON file_deletion_monitoring TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.file_deletion_queue IS 'Queue for scheduling file deletions as part of DPDPA compliance anonymization process';
COMMENT ON FUNCTION create_file_deletion_queue_if_not_exists() IS 'Compatibility function for anonymization process';
COMMENT ON FUNCTION mark_file_deletion_failed(UUID, TEXT) IS 'Mark a file deletion as failed with error message';
COMMENT ON FUNCTION mark_file_deletion_completed(UUID) IS 'Mark a file deletion as successfully completed';
COMMENT ON FUNCTION retry_failed_file_deletions() IS 'Retry failed file deletions that have not exceeded max attempts';
COMMENT ON FUNCTION cleanup_completed_file_deletions(INTEGER) IS 'Clean up old completed file deletion records';
COMMENT ON VIEW file_deletion_monitoring IS 'Monitoring view for file deletion queue status';