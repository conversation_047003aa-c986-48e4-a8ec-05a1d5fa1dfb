-- TEST 3: Sample Data Review - Run this to see actual data and verify it looks correct

-- Show sample records with different scenarios
SELECT 
  '📋 SAMPLE DATA VERIFICATION' as section,
  'Review these records manually to ensure they look correct' as instruction;

-- Sample 1: Doctors with transactions
SELECT 
  '💰 Doctors WITH Transactions' as sample_type,
  id,
  name,
  email,
  billing_status,
  total_paid,
  pending_payments,
  successful_referrals,
  referral_discount_earned,
  referred_by_name,
  current_plan_name,
  current_plan_price
FROM admin_doctors_billing_view 
WHERE total_paid > 0 OR pending_payments > 0
ORDER BY total_paid DESC
LIMIT 3;

-- Sample 2: Doctors without transactions
SELECT 
  '🆓 Doctors WITHOUT Transactions' as sample_type,
  id,
  name,
  email,
  billing_status,
  total_paid,
  pending_payments,
  successful_referrals,
  referral_discount_earned,
  referred_by_name,
  current_plan_name,
  current_plan_price
FROM admin_doctors_billing_view 
WHERE total_paid = 0 AND pending_payments = 0
ORDER BY name
LIMIT 3;

-- Sample 3: Doctors with referrers
SELECT 
  '👥 Doctors WITH Referrers' as sample_type,
  id,
  name,
  email,
  billing_status,
  total_paid,
  pending_payments,
  successful_referrals,
  referral_discount_earned,
  referred_by_name,
  current_plan_name,
  current_plan_price
FROM admin_doctors_billing_view 
WHERE referred_by_name IS NOT NULL
ORDER BY name
LIMIT 3;

-- Sample 4: Doctors without referrers
SELECT 
  '🚫 Doctors WITHOUT Referrers' as sample_type,
  id,
  name,
  email,
  billing_status,
  total_paid,
  pending_payments,
  successful_referrals,
  referral_discount_earned,
  referred_by_name,
  current_plan_name,
  current_plan_price
FROM admin_doctors_billing_view 
WHERE referred_by_name IS NULL
ORDER BY name
LIMIT 3;

-- Summary statistics
SELECT 
  '📊 SUMMARY STATISTICS' as summary,
  COUNT(*) as total_doctors,
  COUNT(CASE WHEN total_paid > 0 THEN 1 END) as doctors_with_payments,
  COUNT(CASE WHEN pending_payments > 0 THEN 1 END) as doctors_with_pending,
  COUNT(CASE WHEN current_plan_name IS NOT NULL THEN 1 END) as doctors_with_plans,
  COUNT(CASE WHEN referred_by_name IS NOT NULL THEN 1 END) as doctors_with_referrers,
  ROUND(AVG(total_paid), 2) as avg_total_paid,
  ROUND(AVG(pending_payments), 2) as avg_pending_payments
FROM admin_doctors_billing_view;