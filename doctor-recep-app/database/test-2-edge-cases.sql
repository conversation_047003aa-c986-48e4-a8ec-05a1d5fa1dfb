-- TEST 2: Edge Cases - Run this to verify all edge cases work properly

-- Test 1: Doctors with NO billing transactions
SELECT 
  '🧪 EDGE CASE: No Transactions' as test_name,
  COUNT(*) as doctors_with_no_transactions,
  COUNT(CASE WHEN total_paid = 0 AND pending_payments = 0 THEN 1 END) as correctly_showing_zero,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN total_paid = 0 AND pending_payments = 0 THEN 1 END) 
    THEN '✅ PASS - All show $0' 
    ELSE '❌ FAIL - Some show wrong amounts' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE NOT EXISTS (
  SELECT 1 FROM billing_transactions bt WHERE bt.doctor_id = v.id
);

-- Test 2: Doctors with NO current plan
SELECT 
  '🧪 EDGE CASE: No Current Plan' as test_name,
  COUNT(*) as doctors_with_no_plan,
  COUNT(CASE WHEN current_plan_name IS NULL AND current_plan_price IS NULL THEN 1 END) as correctly_showing_null,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN current_plan_name IS NULL AND current_plan_price IS NULL THEN 1 END) 
    THEN '✅ PASS - All show NULL plan' 
    ELSE '❌ FAIL - Some show wrong plan data' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE v.id IN (
  SELECT d.id FROM doctors d WHERE d.current_plan_id IS NULL AND d.approved = true
);

-- Test 3: Doctors with NO referrer
SELECT 
  '🧪 EDGE CASE: No Referrer' as test_name,
  COUNT(*) as doctors_with_no_referrer,
  COUNT(CASE WHEN referred_by_name IS NULL THEN 1 END) as correctly_showing_null,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN referred_by_name IS NULL THEN 1 END) 
    THEN '✅ PASS - All show NULL referrer' 
    ELSE '❌ FAIL - Some show wrong referrer data' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE v.id IN (
  SELECT d.id FROM doctors d WHERE d.referred_by IS NULL AND d.approved = true
);

-- Test 4: Doctors with EVERYTHING (transactions, plan, referrer)
SELECT 
  '🧪 EDGE CASE: Complete Data' as test_name,
  COUNT(*) as doctors_with_everything,
  COUNT(CASE WHEN 
    total_paid > 0 AND 
    current_plan_name IS NOT NULL AND 
    referred_by_name IS NOT NULL 
  THEN 1 END) as correctly_showing_all_data,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN 
      total_paid > 0 AND 
      current_plan_name IS NOT NULL AND 
      referred_by_name IS NOT NULL 
    THEN 1 END) 
    THEN '✅ PASS - All show complete data' 
    ELSE '❌ FAIL - Some missing data' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE EXISTS (SELECT 1 FROM billing_transactions bt WHERE bt.doctor_id = v.id AND bt.payment_status = 'paid')
  AND v.current_plan_name IS NOT NULL
  AND v.referred_by_name IS NOT NULL;