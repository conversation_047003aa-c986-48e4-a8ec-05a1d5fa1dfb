-- Comprehensive Test for Billing Optimization
-- Execute this directly in Supabase SQL Editor to validate the optimization

-- =============================================================================
-- TEST 1: Data Accuracy Comparison (Old vs New Implementation)
-- =============================================================================

-- OLD IMPLEMENTATION SIMULATION (what the JavaScript was doing)
WITH old_implementation AS (
  -- Step 1: Get basic doctor data (like the original query)
  SELECT 
    d.id,
    d.name,
    d.email,
    d.clinic_name,
    d.billing_status,
    d.trial_ends_at,
    d.last_payment_date,
    d.next_billing_date,
    d.available_discount_amount,
    d.successful_referrals,
    d.referral_discount_earned,
    d.referred_by,
    bp.name as current_plan_name,
    bp.monthly_price as current_plan_price
  FROM doctors d
  LEFT JOIN billing_plans bp ON d.current_plan_id = bp.id
  WHERE d.approved = true
),
-- Step 2: Calculate payment summaries (like the Promise.all was doing)
old_payment_summaries AS (
  SELECT 
    bt.doctor_id,
    SUM(CASE WHEN bt.payment_status = 'paid' THEN bt.final_amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN bt.payment_status = 'pending' THEN bt.final_amount ELSE 0 END) as pending_payments
  FROM billing_transactions bt
  GROUP BY bt.doctor_id
),
-- Step 3: Get referrer names (like the individual queries were doing)
old_referrer_names AS (
  SELECT 
    d1.id as doctor_id,
    d2.name as referred_by_name
  FROM doctors d1
  LEFT JOIN doctors d2 ON d1.referred_by = d2.id
  WHERE d1.approved = true
),
-- Combine old implementation data
old_final AS (
  SELECT 
    oi.id,
    oi.name,
    oi.email,
    oi.clinic_name,
    oi.billing_status,
    oi.trial_ends_at,
    oi.last_payment_date,
    oi.next_billing_date,
    COALESCE(oi.available_discount_amount, 0) as available_discount_amount,
    oi.current_plan_name,
    oi.current_plan_price,
    COALESCE(ops.total_paid, 0) as total_paid,
    COALESCE(ops.pending_payments, 0) as pending_payments,
    COALESCE(oi.successful_referrals, 0) as successful_referrals,
    COALESCE(oi.referral_discount_earned, 0) as referral_discount_earned,
    orn.referred_by_name
  FROM old_implementation oi
  LEFT JOIN old_payment_summaries ops ON oi.id = ops.doctor_id
  LEFT JOIN old_referrer_names orn ON oi.id = orn.doctor_id
),
-- NEW IMPLEMENTATION (using the view)
new_final AS (
  SELECT 
    id,
    name,
    email,
    clinic_name,
    billing_status,
    trial_ends_at,
    last_payment_date,
    next_billing_date,
    COALESCE(available_discount_amount, 0) as available_discount_amount,
    current_plan_name,
    current_plan_price,
    COALESCE(total_paid, 0) as total_paid,
    COALESCE(pending_payments, 0) as pending_payments,
    COALESCE(successful_referrals, 0) as successful_referrals,
    COALESCE(referral_discount_earned, 0) as referral_discount_earned,
    referred_by_name
  FROM admin_doctors_billing_view
)

-- COMPARISON RESULTS
SELECT 
  'DATA_ACCURACY_TEST' as test_type,
  COUNT(*) as total_records,
  COUNT(CASE WHEN 
    old_final.id = new_final.id AND
    old_final.name = new_final.name AND
    old_final.email = new_final.email AND
    COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
    old_final.billing_status = new_final.billing_status AND
    COALESCE(old_final.trial_ends_at, '1900-01-01'::timestamp) = COALESCE(new_final.trial_ends_at, '1900-01-01'::timestamp) AND
    COALESCE(old_final.last_payment_date, '1900-01-01'::timestamp) = COALESCE(new_final.last_payment_date, '1900-01-01'::timestamp) AND
    COALESCE(old_final.next_billing_date, '1900-01-01'::timestamp) = COALESCE(new_final.next_billing_date, '1900-01-01'::timestamp) AND
    old_final.available_discount_amount = new_final.available_discount_amount AND
    COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
    COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
    old_final.total_paid = new_final.total_paid AND
    old_final.pending_payments = new_final.pending_payments AND
    old_final.successful_referrals = new_final.successful_referrals AND
    old_final.referral_discount_earned = new_final.referral_discount_earned AND
    COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
  THEN 1 END) as matching_records,
  COUNT(*) - COUNT(CASE WHEN 
    old_final.id = new_final.id AND
    old_final.name = new_final.name AND
    old_final.email = new_final.email AND
    COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
    old_final.billing_status = new_final.billing_status AND
    COALESCE(old_final.trial_ends_at, '1900-01-01'::timestamp) = COALESCE(new_final.trial_ends_at, '1900-01-01'::timestamp) AND
    COALESCE(old_final.last_payment_date, '1900-01-01'::timestamp) = COALESCE(new_final.last_payment_date, '1900-01-01'::timestamp) AND
    COALESCE(old_final.next_billing_date, '1900-01-01'::timestamp) = COALESCE(new_final.next_billing_date, '1900-01-01'::timestamp) AND
    old_final.available_discount_amount = new_final.available_discount_amount AND
    COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
    COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
    old_final.total_paid = new_final.total_paid AND
    old_final.pending_payments = new_final.pending_payments AND
    old_final.successful_referrals = new_final.successful_referrals AND
    old_final.referral_discount_earned = new_final.referral_discount_earned AND
    COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
  THEN 1 END) as mismatched_records,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN 
      old_final.id = new_final.id AND
      old_final.name = new_final.name AND
      old_final.email = new_final.email AND
      COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
      old_final.billing_status = new_final.billing_status AND
      COALESCE(old_final.trial_ends_at, '1900-01-01'::timestamp) = COALESCE(new_final.trial_ends_at, '1900-01-01'::timestamp) AND
      COALESCE(old_final.last_payment_date, '1900-01-01'::timestamp) = COALESCE(new_final.last_payment_date, '1900-01-01'::timestamp) AND
      COALESCE(old_final.next_billing_date, '1900-01-01'::timestamp) = COALESCE(new_final.next_billing_date, '1900-01-01'::timestamp) AND
      old_final.available_discount_amount = new_final.available_discount_amount AND
      COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
      COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
      old_final.total_paid = new_final.total_paid AND
      old_final.pending_payments = new_final.pending_payments AND
      old_final.successful_referrals = new_final.successful_referrals AND
      old_final.referral_discount_earned = new_final.referral_discount_earned AND
      COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
    THEN 1 END) THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as test_result
FROM old_final
FULL OUTER JOIN new_final ON old_final.id = new_final.id;

-- =============================================================================
-- TEST 2: Performance Comparison
-- =============================================================================

-- Test the view performance
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
SELECT * FROM admin_doctors_billing_view ORDER BY name;

-- =============================================================================
-- TEST 3: Edge Cases Validation
-- =============================================================================

-- Test doctors with no billing transactions
SELECT 
  'EDGE_CASE_NO_TRANSACTIONS' as test_type,
  COUNT(*) as doctors_with_no_transactions,
  COUNT(CASE WHEN total_paid = 0 AND pending_payments = 0 THEN 1 END) as correctly_handled,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN total_paid = 0 AND pending_payments = 0 THEN 1 END) 
    THEN '✅ PASS' 
    ELSE '❌ FAIL' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE NOT EXISTS (
  SELECT 1 FROM billing_transactions bt WHERE bt.doctor_id = v.id
);

-- Test doctors with no current plan
SELECT 
  'EDGE_CASE_NO_PLAN' as test_type,
  COUNT(*) as doctors_with_no_plan,
  COUNT(CASE WHEN current_plan_name IS NULL AND current_plan_price IS NULL THEN 1 END) as correctly_handled,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN current_plan_name IS NULL AND current_plan_price IS NULL THEN 1 END) 
    THEN '✅ PASS' 
    ELSE '❌ FAIL' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE NOT EXISTS (
  SELECT 1 FROM billing_plans bp WHERE bp.id = (
    SELECT current_plan_id FROM doctors d WHERE d.id = v.id
  )
);

-- Test doctors with no referrer
SELECT 
  'EDGE_CASE_NO_REFERRER' as test_type,
  COUNT(*) as doctors_with_no_referrer,
  COUNT(CASE WHEN referred_by_name IS NULL THEN 1 END) as correctly_handled,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN referred_by_name IS NULL THEN 1 END) 
    THEN '✅ PASS' 
    ELSE '❌ FAIL' 
  END as test_result
FROM admin_doctors_billing_view v
WHERE NOT EXISTS (
  SELECT 1 FROM doctors d2 WHERE d2.id = (
    SELECT referred_by FROM doctors d WHERE d.id = v.id
  )
);

-- =============================================================================
-- TEST 4: Data Consistency Checks
-- =============================================================================

-- Verify payment calculations are correct
WITH manual_calculations AS (
  SELECT 
    d.id,
    d.name,
    COALESCE(SUM(CASE WHEN bt.payment_status = 'paid' THEN bt.final_amount ELSE 0 END), 0) as manual_total_paid,
    COALESCE(SUM(CASE WHEN bt.payment_status = 'pending' THEN bt.final_amount ELSE 0 END), 0) as manual_pending_payments
  FROM doctors d
  LEFT JOIN billing_transactions bt ON d.id = bt.doctor_id
  WHERE d.approved = true
  GROUP BY d.id, d.name
)
SELECT 
  'PAYMENT_CALCULATION_ACCURACY' as test_type,
  COUNT(*) as total_doctors,
  COUNT(CASE WHEN 
    mc.manual_total_paid = v.total_paid AND 
    mc.manual_pending_payments = v.pending_payments 
  THEN 1 END) as correct_calculations,
  COUNT(*) - COUNT(CASE WHEN 
    mc.manual_total_paid = v.total_paid AND 
    mc.manual_pending_payments = v.pending_payments 
  THEN 1 END) as incorrect_calculations,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN 
      mc.manual_total_paid = v.total_paid AND 
      mc.manual_pending_payments = v.pending_payments 
    THEN 1 END) THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as test_result
FROM manual_calculations mc
JOIN admin_doctors_billing_view v ON mc.id = v.id;

-- =============================================================================
-- TEST 5: Sample Data Verification
-- =============================================================================

-- Show a few sample records for manual verification
SELECT 
  'SAMPLE_DATA_VERIFICATION' as test_type,
  id,
  name,
  email,
  billing_status,
  total_paid,
  pending_payments,
  successful_referrals,
  referral_discount_earned,
  referred_by_name,
  current_plan_name,
  current_plan_price
FROM admin_doctors_billing_view 
ORDER BY name 
LIMIT 5;

-- =============================================================================
-- TEST SUMMARY
-- =============================================================================

SELECT 
  '🎯 TEST EXECUTION COMPLETE' as message,
  'Review all test results above' as instruction,
  'All tests should show ✅ PASS for successful optimization' as success_criteria;