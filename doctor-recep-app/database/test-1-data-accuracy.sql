-- TEST 1: Data Accuracy Comparison (Run this first)
-- This is the most critical test - ensures old vs new implementation match exactly

WITH old_implementation AS (
  -- Simulate what the JavaScript was doing
  SELECT 
    d.id,
    d.name,
    d.email,
    d.clinic_name,
    d.billing_status,
    d.trial_ends_at,
    d.last_payment_date,
    d.next_billing_date,
    d.available_discount_amount,
    d.successful_referrals,
    d.referral_discount_earned,
    d.referred_by,
    bp.name as current_plan_name,
    bp.monthly_price as current_plan_price
  FROM doctors d
  LEFT JOIN billing_plans bp ON d.current_plan_id = bp.id
  WHERE d.approved = true
),
old_payment_summaries AS (
  SELECT 
    bt.doctor_id,
    SUM(CASE WHEN bt.payment_status = 'paid' THEN bt.final_amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN bt.payment_status = 'pending' THEN bt.final_amount ELSE 0 END) as pending_payments
  FROM billing_transactions bt
  GROUP BY bt.doctor_id
),
old_referrer_names AS (
  SELECT 
    d1.id as doctor_id,
    d2.name as referred_by_name
  FROM doctors d1
  LEFT JOIN doctors d2 ON d1.referred_by = d2.id
  WHERE d1.approved = true
),
old_final AS (
  SELECT 
    oi.id,
    oi.name,
    oi.email,
    oi.clinic_name,
    oi.billing_status,
    oi.trial_ends_at,
    oi.last_payment_date,
    oi.next_billing_date,
    COALESCE(oi.available_discount_amount, 0) as available_discount_amount,
    oi.current_plan_name,
    oi.current_plan_price,
    COALESCE(ops.total_paid, 0) as total_paid,
    COALESCE(ops.pending_payments, 0) as pending_payments,
    COALESCE(oi.successful_referrals, 0) as successful_referrals,
    COALESCE(oi.referral_discount_earned, 0) as referral_discount_earned,
    orn.referred_by_name
  FROM old_implementation oi
  LEFT JOIN old_payment_summaries ops ON oi.id = ops.doctor_id
  LEFT JOIN old_referrer_names orn ON oi.id = orn.doctor_id
),
new_final AS (
  SELECT 
    id,
    name,
    email,
    clinic_name,
    billing_status,
    trial_ends_at,
    last_payment_date,
    next_billing_date,
    COALESCE(available_discount_amount, 0) as available_discount_amount,
    current_plan_name,
    current_plan_price,
    COALESCE(total_paid, 0) as total_paid,
    COALESCE(pending_payments, 0) as pending_payments,
    COALESCE(successful_referrals, 0) as successful_referrals,
    COALESCE(referral_discount_earned, 0) as referral_discount_earned,
    referred_by_name
  FROM admin_doctors_billing_view
)

-- Show the comparison results
SELECT 
  '🔍 DATA ACCURACY TEST RESULTS' as test_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN 
    old_final.id = new_final.id AND
    old_final.name = new_final.name AND
    old_final.email = new_final.email AND
    COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
    old_final.billing_status = new_final.billing_status AND
    COALESCE(old_final.trial_ends_at::text, '') = COALESCE(new_final.trial_ends_at::text, '') AND
    COALESCE(old_final.last_payment_date::text, '') = COALESCE(new_final.last_payment_date::text, '') AND
    COALESCE(old_final.next_billing_date::text, '') = COALESCE(new_final.next_billing_date::text, '') AND
    old_final.available_discount_amount = new_final.available_discount_amount AND
    COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
    COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
    old_final.total_paid = new_final.total_paid AND
    old_final.pending_payments = new_final.pending_payments AND
    old_final.successful_referrals = new_final.successful_referrals AND
    old_final.referral_discount_earned = new_final.referral_discount_earned AND
    COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
  THEN 1 END) as matching_records,
  COUNT(*) - COUNT(CASE WHEN 
    old_final.id = new_final.id AND
    old_final.name = new_final.name AND
    old_final.email = new_final.email AND
    COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
    old_final.billing_status = new_final.billing_status AND
    COALESCE(old_final.trial_ends_at::text, '') = COALESCE(new_final.trial_ends_at::text, '') AND
    COALESCE(old_final.last_payment_date::text, '') = COALESCE(new_final.last_payment_date::text, '') AND
    COALESCE(old_final.next_billing_date::text, '') = COALESCE(new_final.next_billing_date::text, '') AND
    old_final.available_discount_amount = new_final.available_discount_amount AND
    COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
    COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
    old_final.total_paid = new_final.total_paid AND
    old_final.pending_payments = new_final.pending_payments AND
    old_final.successful_referrals = new_final.successful_referrals AND
    old_final.referral_discount_earned = new_final.referral_discount_earned AND
    COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
  THEN 1 END) as mismatched_records,
  CASE 
    WHEN COUNT(*) = COUNT(CASE WHEN 
      old_final.id = new_final.id AND
      old_final.name = new_final.name AND
      old_final.email = new_final.email AND
      COALESCE(old_final.clinic_name, '') = COALESCE(new_final.clinic_name, '') AND
      old_final.billing_status = new_final.billing_status AND
      COALESCE(old_final.trial_ends_at::text, '') = COALESCE(new_final.trial_ends_at::text, '') AND
      COALESCE(old_final.last_payment_date::text, '') = COALESCE(new_final.last_payment_date::text, '') AND
      COALESCE(old_final.next_billing_date::text, '') = COALESCE(new_final.next_billing_date::text, '') AND
      old_final.available_discount_amount = new_final.available_discount_amount AND
      COALESCE(old_final.current_plan_name, '') = COALESCE(new_final.current_plan_name, '') AND
      COALESCE(old_final.current_plan_price, 0) = COALESCE(new_final.current_plan_price, 0) AND
      old_final.total_paid = new_final.total_paid AND
      old_final.pending_payments = new_final.pending_payments AND
      old_final.successful_referrals = new_final.successful_referrals AND
      old_final.referral_discount_earned = new_final.referral_discount_earned AND
      COALESCE(old_final.referred_by_name, '') = COALESCE(new_final.referred_by_name, '')
    THEN 1 END) THEN '✅ PERFECT MATCH'
    ELSE '❌ DATA MISMATCH DETECTED'
  END as test_result
FROM old_final
FULL OUTER JOIN new_final ON old_final.id = new_final.id;