export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admins: {
        Row: {
          created_at: string
          email: string
          id: string
          name: string
          password_hash: string
          role: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          name: string
          password_hash: string
          role?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          name?: string
          password_hash?: string
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
      consultations: {
        Row: {
          additional_audio_urls: Json | null
          ai_generated_note: string | null
          primary_audio_url: string
          created_at: string
          doctor_id: string | null
          edited_note: string | null
          id: string
          image_urls: Json | null
          patient_number: number | null
          patient_name: string | null
          status: string
          submitted_by: string
          total_file_size_bytes: number | null
          file_retention_until: string | null
          updated_at: string
          consultation_type: string
          doctor_notes: string | null
          additional_notes: string | null
        }
        Insert: {
          additional_audio_urls?: Json | null
          ai_generated_note?: string | null
          primary_audio_url: string
          created_at?: string
          doctor_id?: string | null
          edited_note?: string | null
          id?: string
          image_urls?: Json | null
          patient_number?: number | null
          patient_name?: string | null
          status?: string
          submitted_by: string
          total_file_size_bytes?: number | null
          file_retention_until?: string | null
          updated_at?: string
          consultation_type?: string
          doctor_notes?: string | null
          additional_notes?: string | null
        }
        Update: {
          additional_audio_urls?: Json | null
          ai_generated_note?: string | null
          primary_audio_url?: string
          created_at?: string
          doctor_id?: string | null
          edited_note?: string | null
          id?: string
          image_urls?: Json | null
          patient_number?: number | null
          patient_name?: string | null
          status?: string
          submitted_by?: string
          total_file_size_bytes?: number | null
          file_retention_until?: string | null
          updated_at?: string
          consultation_type?: string
          doctor_notes?: string | null
          additional_notes?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "consultations_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      doctors: {
        Row: {
          approved: boolean
          approved_at: string | null
          approved_by: string | null
          clinic_name: string | null
          created_at: string
          email: string
          id: string
          monthly_quota: number
          name: string
          password_hash: string
          phone: string | null
          quota_reset_at: string
          quota_used: number

          updated_at: string
          referral_code: string | null
          referred_by: string | null
          conversion_date: string | null
          referral_discount_earned: number
          total_referrals: number
          successful_referrals: number
          current_plan_id: string | null
          billing_status: string
          trial_ends_at: string | null
          last_payment_date: string | null
          next_billing_date: string | null
          available_discount_amount: number
        }
        Insert: {
          approved?: boolean
          approved_at?: string | null
          approved_by?: string | null
          clinic_name?: string | null
          created_at?: string
          email: string
          id?: string
          monthly_quota?: number
          name: string
          password_hash: string
          phone?: string | null
          quota_reset_at?: string
          quota_used?: number

          updated_at?: string
          referral_code?: string | null
          referred_by?: string | null
          conversion_date?: string | null
          referral_discount_earned?: number
          total_referrals?: number
          successful_referrals?: number
          current_plan_id?: string | null
          billing_status?: string
          trial_ends_at?: string | null
          last_payment_date?: string | null
          next_billing_date?: string | null
          available_discount_amount?: number
        }
        Update: {
          approved?: boolean
          approved_at?: string | null
          approved_by?: string | null
          clinic_name?: string | null
          created_at?: string
          email?: string
          id?: string
          monthly_quota?: number
          name?: string
          password_hash?: string
          phone?: string | null
          quota_reset_at?: string
          quota_used?: number

          updated_at?: string
          referral_code?: string | null
          referred_by?: string | null
          conversion_date?: string | null
          referral_discount_earned?: number
          total_referrals?: number
          successful_referrals?: number
          current_plan_id?: string | null
          billing_status?: string
          trial_ends_at?: string | null
          last_payment_date?: string | null
          next_billing_date?: string | null
          available_discount_amount?: number
        }
        Relationships: []
      }
      usage_logs: {
        Row: {
          action_type: string
          consultation_id: string | null
          created_at: string
          doctor_id: string | null
          id: string
          metadata: Json | null
          quota_after: number | null
          quota_before: number | null
        }
        Insert: {
          action_type: string
          consultation_id?: string | null
          created_at?: string
          doctor_id?: string | null
          id?: string
          metadata?: Json | null
          quota_after?: number | null
          quota_before?: number | null
        }
        Update: {
          action_type?: string
          consultation_id?: string | null
          created_at?: string
          doctor_id?: string | null
          id?: string
          metadata?: Json | null
          quota_after?: number | null
          quota_before?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_consultation_id_fkey"
            columns: ["consultation_id"]
            isOneToOne: false
            referencedRelation: "consultations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      referral_analytics: {
        Row: {
          id: string
          referrer_id: string | null
          referred_doctor_id: string | null
          referral_code: string
          signup_date: string
          conversion_date: string | null
          discount_earned: number
          status: string
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          referrer_id?: string | null
          referred_doctor_id?: string | null
          referral_code: string
          signup_date?: string
          conversion_date?: string | null
          discount_earned?: number
          status?: string
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          referrer_id?: string | null
          referred_doctor_id?: string | null
          referral_code?: string
          signup_date?: string
          conversion_date?: string | null
          discount_earned?: number
          status?: string
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "referral_analytics_referrer_id_fkey"
            columns: ["referrer_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_analytics_referred_doctor_id_fkey"
            columns: ["referred_doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_plans: {
        Row: {
          id: string
          name: string
          description: string | null
          monthly_price: number
          quota_limit: number
          features: Json | null
          active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          monthly_price: number
          quota_limit: number
          features?: Json | null
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          monthly_price?: number
          quota_limit?: number
          features?: Json | null
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      billing_transactions: {
        Row: {
          id: string
          doctor_id: string
          plan_id: string | null
          amount: number
          discount_amount: number
          final_amount: number
          payment_method: string | null
          payment_status: string
          payment_date: string | null
          billing_period_start: string
          billing_period_end: string
          payment_reference: string | null
          notes: string | null
          created_by: string | null
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          plan_id?: string | null
          amount: number
          discount_amount?: number
          final_amount: number
          payment_method?: string | null
          payment_status?: string
          payment_date?: string | null
          billing_period_start: string
          billing_period_end: string
          payment_reference?: string | null
          notes?: string | null
          created_by?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          plan_id?: string | null
          amount?: number
          discount_amount?: number
          final_amount?: number
          payment_method?: string | null
          payment_status?: string
          payment_date?: string | null
          billing_period_start?: string
          billing_period_end?: string
          payment_reference?: string | null
          notes?: string | null
          created_by?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "billing_transactions_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_transactions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "billing_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      referral_discounts: {
        Row: {
          id: string
          doctor_id: string
          referral_analytics_id: string
          discount_percentage: number
          discount_amount: number
          original_amount: number
          applied_to_transaction_id: string | null
          status: string
          valid_until: string
          applied_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          referral_analytics_id: string
          discount_percentage?: number
          discount_amount: number
          original_amount: number
          applied_to_transaction_id?: string | null
          status?: string
          valid_until: string
          applied_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          referral_analytics_id?: string
          discount_percentage?: number
          discount_amount?: number
          original_amount?: number
          applied_to_transaction_id?: string | null
          status?: string
          valid_until?: string
          applied_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "referral_discounts_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "referral_discounts_referral_analytics_id_fkey"
            columns: ["referral_analytics_id"]
            isOneToOne: false
            referencedRelation: "referral_analytics"
            referencedColumns: ["id"]
          },
        ]
      }
      contact_requests: {
        Row: {
          id: string
          doctor_id: string
          doctor_name: string
          doctor_email: string
          clinic_name: string
          phone_number: string
          request_type: string
          message: string | null
          status: string
          contacted_at: string | null
          resolved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          doctor_id: string
          doctor_name: string
          doctor_email: string
          clinic_name: string
          phone_number: string
          request_type: string
          message?: string | null
          status?: string
          contacted_at?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          doctor_id?: string
          doctor_name?: string
          doctor_email?: string
          clinic_name?: string
          phone_number?: string
          request_type?: string
          message?: string | null
          status?: string
          contacted_at?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contact_requests_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      admin_dashboard_summary: {
        Row: {
          total_doctors: number
          pending_approvals: number
          approved_doctors: number
          total_consultations: number
          total_ai_generations: number
          quota_usage_percentage: number
        }
        Relationships: []
      }
      admin_doctors_with_stats: {
        Row: {
          id: string
          email: string
          name: string
          phone: string | null
          clinic_name: string | null
          monthly_quota: number
          quota_used: number
          quota_reset_at: string
          approved: boolean
          approved_by: string | null
          approved_at: string | null
          referral_code: string | null
          referred_by: string | null
          billing_status: string | null
          trial_ends_at: string | null
          created_at: string
          updated_at: string
          total_consultations: number
          this_month_generations: number
          last_activity: string | null
          quota_percentage: number
        }
        Relationships: []
      }
      admin_doctors_billing_view: {
        Row: {
          id: string
          name: string
          email: string
          clinic_name: string | null
          billing_status: string
          trial_ends_at: string | null
          last_payment_date: string | null
          next_billing_date: string | null
          available_discount_amount: number
          current_plan_name: string | null
          current_plan_price: number | null
          total_paid: number
          pending_payments: number
          successful_referrals: number
          referral_discount_earned: number
          referred_by_name: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      check_and_update_quota: {
        Args: { doctor_uuid: string }
        Returns: boolean
      }
      handle_referral_conversion: {
        Args: { referred_doctor_uuid: string }
        Returns: boolean
      }
      generate_referral_code: {
        Args: { doctor_name: string; doctor_email: string }
        Returns: string
      }
      apply_referral_discount: {
        Args: { transaction_id: string; discount_amount: number }
        Returns: boolean
      }
      complete_payment: {
        Args: { transaction_id: string }
        Returns: boolean
      }
      reset_all_quotas: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      update_doctor_quota: {
        Args: { doctor_uuid: string; new_quota: number; admin_uuid: string }
        Returns: boolean
      }
      set_patient_number: {
        Args: Record<PropertyKey, never>;
        Returns: unknown;
      }
      update_updated_at_column: {
        Args: Record<PropertyKey, never>;
        Returns: unknown;
      }
      get_consultation_stats: {
        Args: { doctor_uuid: string }
        Returns: {
          total_consultations: number
          pending_consultations: number
          approved_consultations: number
          today_consultations: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

// Consultation type enum for different consultation workflows
export type ConsultationType = 'outpatient' | 'discharge' | 'surgery' | 'radiology' | 'dermatology' | 'cardiology_echo' | 'ivf_cycle' | 'pathology';

// Consultation type labels for UI display
export const CONSULTATION_TYPE_LABELS: Record<ConsultationType, string> = {
  outpatient: 'Outpatient Consultation',
  discharge: 'Discharge Summary',
  surgery: 'Operative Note',
  radiology: 'Radiology Report',
  dermatology: 'Dermatology Note',
  cardiology_echo: 'Echocardiogram Report',
  ivf_cycle: 'IVF Cycle Summary',
  pathology: 'Histopathology Report'
};

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"])
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

export type Doctor = Tables<'doctors'>;
export type Consultation = Tables<'consultations'>;
export type Admin = Tables<'admins'>;
export type ReferralAnalytics = Tables<'referral_analytics'>;



export type ApiResponse<T> = { success: true; data: T } | { success: false; error: string };

export interface AdminDashboardStats {
  total_doctors: number;
  pending_approvals: number;
  approved_doctors: number;
  total_consultations: number;
  total_ai_generations: number;
  quota_usage_percentage: number;
}

export interface DashboardStats {
  total_consultations: number;
  pending_consultations: number;
  generated_consultations: number;
  approved_consultations: number;
  today_consultations: number;
}

export interface QuotaInfo {
  monthly_quota: number;
  quota_used: number;
  quota_remaining: number;
  quota_percentage: number;
  quota_reset_at: string;
  days_until_reset: number;
}

export interface ReferralInfo {
  referral_code: string;
  total_referrals: number;
  successful_referrals: number;
  pending_referrals: number;
  discount_earned: number;
  referred_by?: {
    name: string;
    referral_code: string;
  } | null;
  recent_referrals: Array<{
    id: string;
    name: string;
    email: string;
    signup_date: string;
    conversion_date: string | null;
    status: 'pending' | 'converted' | 'expired';
  }>;
}

export type DoctorWithStats = Omit<Tables<'doctors'>, 'consultations' | 'phone' | 'clinic_name' | 'approved_by' | 'approved_at' | 'template_config'> & {
  phone?: string;
  clinic_name?: string;
  approved_by?: string;
  approved_at?: string;
  total_consultations: number;
  this_month_generations: number;
  quota_percentage: number;
  last_activity?: string;
};

// Type for the admin_doctors_with_stats view
export type AdminDoctorWithStats = {
  id: string;
  email: string;
  name: string;
  phone?: string;
  clinic_name?: string;
  monthly_quota: number;
  quota_used: number;
  quota_reset_at: string;
  approved: boolean;
  approved_by?: string;
  approved_at?: string;
  referral_code?: string;
  referred_by?: string;
  billing_status?: string;
  trial_ends_at?: string;
  created_at: string;
  updated_at: string;
  total_consultations: number;
  this_month_generations: number;
  last_activity?: string;
  quota_percentage: number;
};

export type AdminActionRequest =
  | { action: 'approve'; doctor_id: string }
  | { action: 'reject'; doctor_id: string }
  | { action: 'update_quota'; doctor_id: string; data: { quota: number; reason?: string } }
  | { action: 'disable'; doctor_id: string }
  | { action: 'enable'; doctor_id: string };

export interface FormState {
  success: boolean;
  message: string;
  fieldErrors?: { [key: string]: string[] };
}

export interface ImageFile {
  id: string;
  file: File;
  preview?: string; // Optional for optimistic UI - not needed when waiting for final state
  name: string;
  type: string;
  size: number;
}

export interface ImageCaptureState {
  images: ImageFile[];
  status?: 'idle' | 'capturing' | 'uploaded' | 'error';
  error: string | null;
}

export interface AudioRecordingState {
  isRecording: boolean;
  duration: number;
  audioBlob?: Blob;
  audioFile?: File;
  error?: string | null;
  status?: 'idle' | 'recording' | 'recorded' | 'uploading' | 'uploaded' | 'error';
}

export interface SessionPayload {
  userId: string;
  expiresAt: Date;
  adminId?: string;
  role?: 'admin' | 'super_admin';
}

