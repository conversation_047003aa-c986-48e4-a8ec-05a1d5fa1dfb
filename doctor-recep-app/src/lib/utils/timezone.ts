/**
 * Timezone utilities for handling IST (Indian Standard Time) in the application
 * IST = UTC+5:30
 */

export const IST_OFFSET_MS = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds

/**
 * Get current time in IST
 */
export function getCurrentIST(): Date {
  const now = new Date();
  return new Date(now.getTime() + IST_OFFSET_MS);
}

/**
 * Convert a UTC date to IST
 */
export function utcToIST(utcDate: Date): Date {
  return new Date(utcDate.getTime() + IST_OFFSET_MS);
}

/**
 * Convert an IST date to UTC
 */
export function istToUTC(istDate: Date): Date {
  return new Date(istDate.getTime() - IST_OFFSET_MS);
}

/**
 * Get date range for a specific day in IST timezone
 * Returns UTC timestamps for database queries
 */
export function getISTDateRange(date: Date): { start: string; end: string } {
  // Create start of day in IST (00:00:00 IST)
  const startOfDayIST = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  // Create end of day in IST (23:59:59.999 IST)
  const endOfDayIST = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
  
  // Convert IST times to UTC for database query
  const startUTC = istToUTC(startOfDayIST);
  const endUTC = istToUTC(endOfDayIST);
  
  return {
    start: startUTC.toISOString(),
    end: endUTC.toISOString()
  };
}

/**
 * Parse a date string (YYYY-MM-DD) as IST date
 */
export function parseCustomDateAsIST(dateString: string): Date {
  const dateParts = dateString.split('-');
  return new Date(
    parseInt(dateParts[0]), // year
    parseInt(dateParts[1]) - 1, // month (0-indexed)
    parseInt(dateParts[2]) // day
  );
}

/**
 * Format a date for display in IST
 */
export function formatDateIST(date: Date): string {
  const istDate = utcToIST(date);
  return istDate.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    timeZone: 'Asia/Kolkata'
  });
}

/**
 * Debug function to log timezone conversions
 */
export function debugTimezone(label: string, utcDate: Date) {
  const istDate = utcToIST(utcDate);
  console.log(`[${label}] UTC: ${utcDate.toISOString()} | IST: ${istDate.toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}`);
}