'use server'

import { createClient } from '@/lib/supabase/server'
import { ReferralInfo, ApiResponse } from '@/lib/types'
import { revalidatePath } from 'next/cache'

export async function getReferralInfo(doctorId: string): Promise<ApiResponse<ReferralInfo>> {
  try {
    const supabase = await createClient()

    // Get doctor's referral information
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select(`
        referral_code,
        total_referrals,
        successful_referrals,
        referral_discount_earned,
        referred_by
      `)
      .eq('id', doctorId)
      .single()

    if (doctorError || !doctor) {
      return { success: false, error: 'Failed to fetch referral information' }
    }

    // Get pending referrals count
    const { count: pendingCount } = await supabase
      .from('referral_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('referrer_id', doctorId)
      .eq('status', 'pending')

    // Get recent referrals
    const { data: recentReferrals, error: referralsError } = await supabase
      .from('referral_analytics')
      .select(`
        id,
        referred_doctor:doctors!referral_analytics_referred_doctor_id_fkey(name, email),
        signup_date,
        conversion_date,
        status
      `)
      .eq('referrer_id', doctorId)
      .order('created_at', { ascending: false })
      .limit(10)

    if (referralsError) {
      return { success: false, error: 'Failed to fetch recent referrals' }
    }

    // Get referrer info separately if exists
    let referredBy = null
    if (doctor.referred_by) {
      const { data: referrer } = await supabase
        .from('doctors')
        .select('name, referral_code')
        .eq('id', doctor.referred_by)
        .single()
      
      if (referrer) {
        referredBy = {
          name: referrer.name,
          referral_code: referrer.referral_code || ''
        }
      }
    }

    const referralInfo: ReferralInfo = {
      referral_code: doctor.referral_code || '',
      total_referrals: doctor.total_referrals || 0,
      successful_referrals: doctor.successful_referrals || 0,
      pending_referrals: pendingCount || 0,
      discount_earned: doctor.referral_discount_earned || 0,
      referred_by: referredBy,
      recent_referrals: (recentReferrals || []).map(ref => ({
        id: ref.id,
        name: ref.referred_doctor?.name || 'Unknown',
        email: ref.referred_doctor?.email || 'Unknown',
        signup_date: ref.signup_date,
        conversion_date: ref.conversion_date,
        status: ref.status as 'pending' | 'converted' | 'expired'
      }))
    }

    return { success: true, data: referralInfo }
  } catch (error) {
    console.error('Error fetching referral info:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function generateReferralLink(doctorId: string): Promise<ApiResponse<string>> {
  try {
    const supabase = await createClient()

    const { data: doctor, error } = await supabase
      .from('doctors')
      .select('referral_code')
      .eq('id', doctorId)
      .single()

    if (error || !doctor?.referral_code) {
      return { success: false, error: 'Failed to fetch referral code' }
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://celerai.vercel.app'
    const referralLink = `${baseUrl}/signup?ref=${doctor.referral_code}`

    return { success: true, data: referralLink }
  } catch (error) {
    console.error('Error generating referral link:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function processReferralSignup(referralCode: string, newDoctorId: string): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    // Find the referrer
    const { data: referrer, error: referrerError } = await supabase
      .from('doctors')
      .select('id, name')
      .eq('referral_code', referralCode)
      .single()

    if (referrerError || !referrer) {
      return { success: false, error: 'Invalid referral code' }
    }

    // Update the new doctor with referrer information
    const { error: updateError } = await supabase
      .from('doctors')
      .update({ referred_by: referrer.id })
      .eq('id', newDoctorId)

    if (updateError) {
      return { success: false, error: 'Failed to process referral signup' }
    }

    // Create referral analytics record
    const { error: analyticsError } = await supabase
      .from('referral_analytics')
      .insert({
        referrer_id: referrer.id,
        referred_doctor_id: newDoctorId,
        referral_code: referralCode,
        status: 'pending'
      })

    if (analyticsError) {
      console.error('Failed to create analytics record:', analyticsError)
      // Don't fail the signup for this
    }

    // Update referrer's total referrals count
    const { data: currentReferrer } = await supabase
      .from('doctors')
      .select('total_referrals')
      .eq('id', referrer.id)
      .single()

    if (currentReferrer) {
      const { error: countError } = await supabase
        .from('doctors')
        .update({ total_referrals: (currentReferrer.total_referrals || 0) + 1 })
        .eq('id', referrer.id)

      if (countError) {
        console.error('Failed to update referral count:', countError)
      }
    }

    return { success: true, data: true }
  } catch (error) {
    console.error('Error processing referral signup:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function markReferralConversion(doctorId: string): Promise<ApiResponse<boolean>> {
  try {
    const supabase = await createClient()

    // Call the database function to handle conversion
    const { data, error } = await supabase.rpc('handle_referral_conversion', {
      referred_doctor_uuid: doctorId
    })

    if (error) {
      console.error('Error marking referral conversion:', error)
      return { success: false, error: 'Failed to process referral conversion' }
    }

    revalidatePath('/dashboard')
    revalidatePath('/admin/dashboard')

    return { success: true, data: data || false }
  } catch (error) {
    console.error('Error marking referral conversion:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function getAdminReferralStats(): Promise<ApiResponse<{
  total_referrals: number;
  successful_conversions: number;
  pending_referrals: number;
  total_discount_earned: number;
  top_referrers: Array<{
    id: string;
    name: string;
    referral_code: string;
    successful_referrals: number;
    discount_earned: number;
  }>;
}>> {
  try {
    const supabase = await createClient()

    // Get overall stats
    const { data: totalStats, error: statsError } = await supabase
      .from('referral_analytics')
      .select('status, discount_earned')

    if (statsError) {
      return { success: false, error: 'Failed to fetch referral statistics' }
    }

    const totalReferrals = totalStats?.length || 0
    const successfulConversions = totalStats?.filter(s => s.status === 'converted').length || 0
    const pendingReferrals = totalStats?.filter(s => s.status === 'pending').length || 0
    const totalDiscountEarned = totalStats?.reduce((sum, s) => sum + (s.discount_earned || 0), 0) || 0

    // Get top referrers
    const { data: topReferrers, error: referrersError } = await supabase
      .from('doctors')
      .select('id, name, referral_code, successful_referrals, referral_discount_earned')
      .gt('successful_referrals', 0)
      .order('successful_referrals', { ascending: false })
      .limit(10)

    if (referrersError) {
      return { success: false, error: 'Failed to fetch top referrers' }
    }

    return {
      success: true,
      data: {
        total_referrals: totalReferrals,
        successful_conversions: successfulConversions,
        pending_referrals: pendingReferrals,
        total_discount_earned: totalDiscountEarned,
        top_referrers: (topReferrers || []).map(r => ({
          id: r.id,
          name: r.name,
          referral_code: r.referral_code || '',
          successful_referrals: r.successful_referrals || 0,
          discount_earned: r.referral_discount_earned || 0
        }))
      }
    }
  } catch (error) {
    console.error('Error fetching admin referral stats:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

export async function validateReferralCode(referralCode: string): Promise<ApiResponse<{
  valid: boolean;
  referrer_name?: string;
}>> {
  try {
    const supabase = await createClient()

    const { data: referrer, error } = await supabase
      .from('doctors')
      .select('name, approved')
      .eq('referral_code', referralCode)
      .eq('approved', true)
      .single()

    if (error || !referrer) {
      return { 
        success: true, 
        data: { valid: false } 
      }
    }

    return {
      success: true,
      data: {
        valid: true,
        referrer_name: referrer.name
      }
    }
  } catch (error) {
    console.error('Error validating referral code:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}