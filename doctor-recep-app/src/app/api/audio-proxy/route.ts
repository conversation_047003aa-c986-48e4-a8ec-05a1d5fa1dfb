import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const audioUrl = searchParams.get('url')

    if (!audioUrl) {
      return NextResponse.json(
        { error: 'Missing audio URL parameter' },
        { status: 400 }
      )
    }

    // Validate that the URL is from our trusted R2 domain
    const allowedDomains = [
      'celerai.tallyup.pro',
      process.env.R2_PUBLIC_URL?.replace('https://', ''),
    ].filter(Boolean)

    const urlObj = new URL(audioUrl)
    if (!allowedDomains.some(domain => urlObj.hostname === domain)) {
      return NextResponse.json(
        { error: 'Unauthorized audio source' },
        { status: 403 }
      )
    }

    // Fetch the audio file from R2
    const response = await fetch(audioUrl, {
      headers: {
        'User-Agent': 'CelerAI-AudioProxy/1.0',
      },
    })

    if (!response.ok) {
      console.error(`Failed to fetch audio: ${response.status} ${response.statusText}`)
      return NextResponse.json(
        { error: 'Failed to fetch audio file' },
        { status: response.status }
      )
    }

    // Get the content type from the original response
    const contentType = response.headers.get('Content-Type') || 'audio/webm'
    const contentLength = response.headers.get('Content-Length')

    // Create response headers with CORS support
    const headers = new Headers({
      'Content-Type': contentType,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    })

    if (contentLength) {
      headers.set('Content-Length', contentLength)
    }

    // Stream the audio data through the proxy
    return new NextResponse(response.body, {
      status: 200,
      headers,
    })

  } catch (error) {
    console.error('Audio proxy error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
