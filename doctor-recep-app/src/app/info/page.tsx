import { Metadata } from 'next'
import { Suspense } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { verifySession } from '@/lib/auth/dal'
import { InfoData } from '@/components/data/info-data'
import { InfoPageSkeleton } from '@/components/ui/skeleton-loaders'
import { <PERSON>rk<PERSON>, BarChart3 } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Info - Celer AI',
  description: 'View statistics, quota information, and referral details',
}

export default async function InfoPage() {
  // OPTIMIZED: Only verify session (fast), then stream the rest
  const session = await verifySession()

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/dashboard"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Dashboard
            </Link>
            <Link
              href="/settings"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Settings
            </Link>
          </div>
        </div>
      </nav>

      <main className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
              Analytics
            </span>
          </h1>

          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12">
            <BarChart3 className="w-4 h-4 text-indigo-600 animate-pulse" />
            <span className="text-indigo-700 text-sm font-medium">Analytics & Insights</span>
            <Sparkles className="w-4 h-4 text-purple-600" />
          </div>
        </div>

        {/* STREAMING: Data loads progressively while user sees immediate structure */}
        <Suspense fallback={<InfoPageSkeleton />}>
          <InfoData userId={session.userId} />
        </Suspense>
      </main>
    </div>
  )
}