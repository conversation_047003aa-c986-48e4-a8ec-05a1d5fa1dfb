import { Metadata } from 'next'
import { verifySession, getUser } from '@/lib/auth/dal'
import { TemplatesInterface } from '@/components/templates/templates-interface'

export const metadata: Metadata = {
  title: 'Templates - Celer AI',
  description: 'Create and manage custom consultation templates',
}

export default async function TemplatesPage() {
  const session = await verifySession()
  
  const user = await getUser()

  return (
    <TemplatesInterface 
      user={user} 
      doctorId={session.userId}
    />
  )
}
